site_name: DSPy
site_description: The framework for programming—rather than prompting—language models.
site_url: https://dspy.ai/

repo_url: https://github.com/stanfordnlp/dspy
repo_name: stanfordnlp/dspy

edit_uri: blob/main/docs/docs/
docs_dir: "docs/"

nav:
    - Get Started: index.md
    - Learn DSPy:
        - Learning DSPy: learn/index.md
        - DSPy Programming:
            - Programming Overview: learn/programming/overview.md
            - Language Models: learn/programming/language_models.md
            - Signatures: learn/programming/signatures.md
            - Modules: learn/programming/modules.md
        - DSPy Evaluation:
            - Evaluation Overview: learn/evaluation/overview.md
            - Data Handling: learn/evaluation/data.md
            - Metrics: learn/evaluation/metrics.md
        - DSPy Optimization:
            - Optimization Overview: learn/optimization/overview.md
            - Optimizers: learn/optimization/optimizers.md
    - Tutorials:
        - Tutorials Overview: tutorials/index.md
        - Build AI Programs with DSPy:
            - Overview: tutorials/build_ai_program/index.md
            - Managing Conversation History: tutorials/conversation_history/index.md
            - Building AI Agents with DSPy: tutorials/customer_service_agent/index.ipynb
            - Building AI Applications by Customizing DSPy Modules: tutorials/custom_module/index.ipynb
            - Retrieval-Augmented Generation (RAG): tutorials/rag/index.ipynb
            - Building RAG as Agent: tutorials/agents/index.ipynb
            - Entity Extraction: tutorials/entity_extraction/index.ipynb
            - Classification: tutorials/classification/index.md
            - Multi-Hop RAG: tutorials/multihop_search/index.ipynb
            - Privacy-Conscious Delegation: tutorials/papillon/index.md
            - Program Of Thought: tutorials/program_of_thought/index.ipynb
            - Image Generation Prompt iteration: tutorials/image_generation_prompting/index.ipynb
            - Audio: tutorials/audio/index.ipynb
        - Optimize AI Programs with DSPy:
            - Overview: tutorials/optimize_ai_program/index.md
            - Math Reasoning: tutorials/math/index.ipynb
            - Classification Finetuning: tutorials/classification_finetuning/index.ipynb
            - Advanced Tool Use: tutorials/tool_use/index.ipynb
            - Finetuning Agents: tutorials/games/index.ipynb
        - Experimental RL Optimization for DSPy:
            - Overview: tutorials/rl_ai_program/index.md
            - RL for Privacy-Conscious Delegation: tutorials/rl_papillon/index.ipynb
            - RL for Multi-Hop Research: tutorials/rl_multihop/index.ipynb
        - Tools, Development, and Deployment:
            - Overview: tutorials/core_development/index.md
            - Use MCP in DSPy: tutorials/mcp/index.md
            - Output Refinement: tutorials/output_refinement/best-of-n-and-refine.md
            - Saving and Loading: tutorials/saving/index.md
            - Cache: tutorials/cache/index.md
            - Deployment: tutorials/deployment/index.md
            - Debugging & Observability: tutorials/observability/index.md
            - Tracking DSPy Optimizers: tutorials/optimizer_tracking/index.md
            - Streaming: tutorials/streaming/index.md
            - Async: tutorials/async/index.md
        - Real-World Examples:
            - Overview: tutorials/real_world_examples/index.md
            - Generating llms.txt: tutorials/llms_txt_generation/index.md
            - Memory-Enabled ReAct Agents: tutorials/mem0_react_agent/index.md
            - Financial Analysis with Yahoo Finance: tutorials/yahoo_finance_react/index.md
            - Email Information Extraction: tutorials/email_extraction/index.md
            - Code Generation for Unfamiliar Libraries: tutorials/sample_code_generation/index.md
            - Building a Creative Text-Based AI Game: tutorials/ai_text_game/index.md
    - DSPy in Production: production/index.md
    - Community:
        - Community Resources: community/community-resources.md
        - Use Cases: community/use-cases.md
        - Contributing: community/how-to-contribute.md
    - FAQ:
        - FAQ: faqs.md
        - Cheatsheet: cheatsheet.md

    - API Reference:
        - API Reference: api/index.md
        - Adapters:
            - Adapter: api/adapters/Adapter.md
            - ChatAdapter: api/adapters/ChatAdapter.md
            - JSONAdapter: api/adapters/JSONAdapter.md
            - TwoStepAdapter: api/adapters/TwoStepAdapter.md
        - Evaluation:
            - CompleteAndGrounded: api/evaluation/CompleteAndGrounded.md
            - Evaluate: api/evaluation/Evaluate.md
            - SemanticF1: api/evaluation/SemanticF1.md
            - answer_exact_match: api/evaluation/answer_exact_match.md
            - answer_passage_match: api/evaluation/answer_passage_match.md
        - Models:
            - Embedder: api/models/Embedder.md
            - LM: api/models/LM.md
        - Modules:
            - BestOfN: api/modules/BestOfN.md
            - ChainOfThought: api/modules/ChainOfThought.md
            - CodeAct: api/modules/CodeAct.md
            - Module: api/modules/Module.md
            - MultiChainComparison: api/modules/MultiChainComparison.md
            - Parallel: api/modules/Parallel.md
            - Predict: api/modules/Predict.md
            - ProgramOfThought: api/modules/ProgramOfThought.md
            - ReAct: api/modules/ReAct.md
            - Refine: api/modules/Refine.md
        - Optimizers:
            - BetterTogether: api/optimizers/BetterTogether.md
            - BootstrapFewShot: api/optimizers/BootstrapFewShot.md
            - BootstrapFewShotWithRandomSearch: api/optimizers/BootstrapFewShotWithRandomSearch.md
            - BootstrapFinetune: api/optimizers/BootstrapFinetune.md
            - BootstrapRS: api/optimizers/BootstrapRS.md
            - COPRO: api/optimizers/COPRO.md
            - Ensemble: api/optimizers/Ensemble.md
            - InferRules: api/optimizers/InferRules.md
            - KNN: api/optimizers/KNN.md
            - KNNFewShot: api/optimizers/KNNFewShot.md
            - LabeledFewShot: api/optimizers/LabeledFewShot.md
            - MIPROv2: api/optimizers/MIPROv2.md
            - SIMBA: api/optimizers/SIMBA.md
        - Primitives:
            - Example: api/primitives/Example.md
            - History: api/primitives/History.md
            - Image: api/primitives/Image.md
            - Prediction: api/primitives/Prediction.md
            - Tool: api/primitives/Tool.md
        - Signatures:
            - InputField: api/signatures/InputField.md
            - OutputField: api/signatures/OutputField.md
            - Signature: api/signatures/Signature.md
        - Tools:
            - ColBERTv2: api/tools/ColBERTv2.md
            - Embeddings: api/tools/Embeddings.md
            - PythonInterpreter: api/tools/PythonInterpreter.md
        - Utils:
            - StatusMessage: api/utils/StatusMessage.md
            - StatusMessageProvider: api/utils/StatusMessageProvider.md
            - StreamListener: api/utils/StreamListener.md
            - asyncify: api/utils/asyncify.md
            - configure_cache: api/utils/configure_cache.md
            - disable_litellm_logging: api/utils/disable_litellm_logging.md
            - disable_logging: api/utils/disable_logging.md
            - enable_litellm_logging: api/utils/enable_litellm_logging.md
            - enable_logging: api/utils/enable_logging.md
            - inspect_history: api/utils/inspect_history.md
            - load: api/utils/load.md
            - streamify: api/utils/streamify.md

theme:
    name: material
    custom_dir: overrides
    features:
        - navigation.tabs
        - navigation.path
        - navigation.indexes
        - navigation.expand
        - toc.follow
        - navigation.top
        - search.suggest
        - search.highlight
        - content.tabs.link
        - content.code.annotation
        - content.code.copy
        - navigation.footer
        - content.action.edit
    language: en
    palette:
        - scheme: default
          toggle:
            icon: material/weather-night
            name: Switch to dark mode
          primary: white
          accent: black
        - scheme: slate
          toggle:
            icon: material/weather-sunny
            name: Switch to light mode
          primary: black
          accent: lime
    icon:
        repo: fontawesome/brands/git-alt
        edit: material/pencil
        view: material/eye
    logo: static/img/dspy_logo.png
    favicon: static/img/logo.png

extra_css:
    - stylesheets/extra.css

plugins:
    - social
    - search:
        lang: en
        separator: '[\s\-\.]+'
    - mkdocstrings:
        handlers:
            python:
                options:
                    docstring_style: google
                    show_source: true
                    show_root_heading: true
                    heading_level: 3
                    members_order: source
                    separate_signature: false
                    show_category_heading: true
                    show_symbol_type_heading: true
                    show_docstring_parameters: true
                    show_if_no_docstring: true
                    show_signature_annotations: true
                    unwrap_annotated: true
                    annotations_path: brief
                    docstring_section_style: table
                    merge_init_into_class: true
                    rendering:
                        show_if_no_docstring: true
                        show_warnings: false
                        html_meta: false
    - mkdocs-jupyter:
        ignore_h1_titles: true
    - redirects:
        redirect_maps:
            # Redirect /intro/ to the main page
            "intro/index.md": "index.md"
            "intro.md": "index.md"
            
            "deep-dive/optimizers/bootstrap-fewshot.md": "api/optimizers/BootstrapFewShot.md"
            "deep-dive/optimizers/bfrs.md": "api/optimizers/BootstrapFewShotWithRandomSearch.md"
            "deep-dive/optimizers/BootstrapFinetune.md": "api/optimizers/BootstrapFinetune.md"
            "deep-dive/optimizers/copro.md": "api/optimizers/COPRO.md"
            "deep-dive/optimizers/Ensemble.md": "api/optimizers/Ensemble.md"
            "deep-dive/optimizers/LabeledFewShot.md": "api/optimizers/LabeledFewShot.md"
            "deep-dive/optimizers/miprov2.md": "api/optimizers/MIPROv2.md"

            "docs/quick-start/getting-started-01.md": "tutorials/rag/index.ipynb"
            "docs/quick-start/getting-started-02.md": "tutorials/rag/index.ipynb"
            "quick-start/getting-started-01.md": "tutorials/rag/index.ipynb"
            "quick-start/getting-started-02.md": "tutorials/rag/index.ipynb"

extra:
    social:
        - icon: fontawesome/brands/github
          link: https://github.com/stanfordnlp/dspy
        - icon: fontawesome/brands/discord
          link: https://discord.gg/XCGy2WDCQB

extra_javascript:
    - "js/runllm-widget.js"

markdown_extensions:
    - pymdownx.tabbed:
        alternate_style: true
    - pymdownx.highlight:
        anchor_linenums: true
    - pymdownx.inlinehilite
    - pymdownx.snippets
    - admonition
    - pymdownx.arithmatex:
        generic: true
    - footnotes
    - pymdownx.details
    - pymdownx.superfences
    - pymdownx.mark
    - attr_list
    - md_in_html
    - pymdownx.emoji:
        emoji_index: !!python/name:material.extensions.emoji.twemoji
        emoji_generator: !!python/name:material.extensions.emoji.to_svg

copyright: |
    &copy; 2025 <a href="https://github.com/stanfordnlp"  target="_blank" rel="noopener">DSPy</a>