default_language_version:
  python: python3.10

default_stages: [pre-commit]
default_install_hook_types: [pre-commit]

repos:
  - repo: local
    hooks:
      - id: ruff-check
        name: ruff (lint)
        entry: ruff
        language: system
        types_or: [python, pyi]
        files: ^(dspy|tests)/.*\.py$
        exclude: ^(dspy/__metadata__\.py|tests/reliability/.*\.py)$
        args: [check, --fix-only]

  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: check-yaml
        args: ["--allow-multiple-documents", "--unsafe"]
      - id: check-toml
      - id: check-added-large-files
        args: ["--maxkb=1024"]
      - id: check-merge-conflict
      - id: debug-statements
